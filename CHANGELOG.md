## Contributing

Please read the guidelines in the [contributing docs](https://contribute.freecodecamp.org/#/how-to-work-on-tutorials-that-use-coderoad) before contributings. Contributions to this project needs to follow the correct workflow.

# Change Log

Whenever something on the version branch gets changed, add the new branch name and the changes here.

## [v1.0.0]

- Initial soft release with news article

## [v1.0.1]

- Remove auto-save from VSCode settings
- Change Project name in VSCode settings

## [v1.0.2]

- Move startup commands to `setup.sh`
- Run `setup.sh` on continue and reset

## [v1.0.3]

- Add `exit` flag to mocha
- [Don't use strict equal on tests with numbers](https://github.com/freeCodeCamp/freeCodeCamp/issues/45687)

## [v1.0.4]

- Add `atomic_mass.txt` file to boilerplate files so campers can know what the values are if they mess up, and changed test text to mention that
- Clarify in the test text that the script should provide only the suggested output and nothing else

## [v2.0.0]

- Add <PERSON>d config
