image: gitpod/workspace-postgres:2024-01-24-09-19-42

workspaceLocation: 'project'
checkoutLocation: 'project'

tasks:
  - before: |
      sudo cp /workspace/project/.freeCodeCamp/.bashrc ~/.bashrc
      sudo cp /workspace/project/.freeCodeCamp/pg_hba.conf /etc/postgresql/12/main/pg_hba.conf
      sudo touch /workspace/.bash_history
      sudo chmod -R 777 /workspace
      sudo chown -R postgres:postgres /var/lib/postgresql/12/main
      echo -e '\nexport GIT_EDITOR=/usr/bin/nano' >> $HOME/.bashrc

    command: |
      sudo rm /workspace/project/CHANGELOG.md
      sudo rm /workspace/project/coderoad.yaml
      sudo rm /workspace/project/tutorial.json
      sudo rm /workspace/project/TUTORIAL.md
      pg_stop && sudo service postgresql start && echo "SELECT 'CREATE USER freecodecamp WITH CREATEDB' WHERE NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname='freecodecamp')\gexec" | psql -U postgres -X
      exit

vscode:
  extensions:
    - CodeRoad.coderoad
