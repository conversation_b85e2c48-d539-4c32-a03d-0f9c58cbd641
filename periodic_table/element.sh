#!/bin/bash

PSQL="psql --username=freecodecamp --dbname=periodic_table -t --no-align -c"

if [[ -z $1 ]]
then
  echo "Please provide an element as an argument."
  exit 0
fi

ARG=$1

# Determine if argument is atomic number (integer), symbol (1-2 letters), or name (string)
if [[ $ARG =~ ^[0-9]+$ ]]
then
  QUERY="SELECT e.atomic_number, e.name, e.symbol, t.type, p.atomic_mass, p.melting_point_celsius, p.boiling_point_celsius
         FROM elements e
         JOIN properties p ON e.atomic_number = p.atomic_number
         JOIN types t ON p.type_id = t.type_id
         WHERE e.atomic_number = $ARG;"
elif [[ $ARG =~ ^[A-Za-z]{1,2}$ ]]
then
  # Capitalize first letter, lowercase second letter if exists
  ARG_CAP="$(tr '[:lower:]' '[:upper:]' <<< ${ARG:0:1})$(tr '[:upper:]' '[:lower:]' <<< ${ARG:1:1})"
  QUERY="SELECT e.atomic_number, e.name, e.symbol, t.type, p.atomic_mass, p.melting_point_celsius, p.boiling_point_celsius
         FROM elements e
         JOIN properties p ON e.atomic_number = p.atomic_number
         JOIN types t ON p.type_id = t.type_id
         WHERE e.symbol = '$ARG_CAP';"
else
  # Capitalize first letter of name argument
  ARG_CAP="$(tr '[:lower:]' '[:upper:]' <<< ${ARG:0:1})${ARG:1}"
  QUERY="SELECT e.atomic_number, e.name, e.symbol, t.type, p.atomic_mass, p.melting_point_celsius, p.boiling_point_celsius
         FROM elements e
         JOIN properties p ON e.atomic_number = p.atomic_number
         JOIN types t ON p.type_id = t.type_id
         WHERE e.name = '$ARG_CAP';"
fi

RESULT=$($PSQL "$QUERY")

if [[ -z $RESULT ]]
then
  echo "I could not find that element in the database."
  exit 0
fi

# Parse result
IFS="|" read ATOMIC_NUMBER NAME SYMBOL TYPE MASS MELTING BOILING <<< "$RESULT"

echo "The element with atomic number $ATOMIC_NUMBER is $NAME ($SYMBOL). It's a $TYPE, with a mass of $MASS amu. $NAME has a melting point of $MELTING celsius and a boiling point of $BOILING celsius."
