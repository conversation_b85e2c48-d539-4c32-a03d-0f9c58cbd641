-- Rename columns in elements and properties tables
ALTER TABLE elements RENAME COLUMN weight TO atomic_mass;
ALTER TABLE properties RENAME COLUMN melting_point TO melting_point_celsius;
ALTER TABLE properties RENAME COLUMN boiling_point TO boiling_point_celsius;

-- Set NOT NULL constraints on melting_point_celsius and boiling_point_celsius
ALTER TABLE properties ALTER COLUMN melting_point_celsius SET NOT NULL;
ALTER TABLE properties ALTER COLUMN boiling_point_celsius SET NOT NULL;

-- Add UNIQUE and NOT NULL constraints to symbol and name columns in elements table
ALTER TABLE elements
  ALTER COLUMN symbol SET NOT NULL,
  ALTER COLUMN name SET NOT NULL;

ALTER TABLE elements
  ADD CONSTRAINT unique_symbol UNIQUE (symbol),
  ADD CONSTRAINT unique_name UNIQUE (name);

-- Set atomic_number in properties as foreign key referencing elements
ALTER TABLE properties
  ADD CONSTRAINT fk_atomic_number FOREIGN KEY (atomic_number) REFERENCES elements(atomic_number);

-- Create types table
CREATE TABLE types (
  type_id SERIAL PRIMARY KEY,
  type VARCHAR NOT NULL
);

-- Insert the three types from properties table into types table
INSERT INTO types (type)
SELECT DISTINCT type FROM properties;

-- Add type_id column to properties table with NOT NULL constraint
ALTER TABLE properties ADD COLUMN type_id INT NOT NULL;

-- Update properties.type_id to link to types.type_id
UPDATE properties
SET type_id = types.type_id
FROM types
WHERE properties.type = types.type;

-- Remove the type column from properties table
ALTER TABLE properties DROP COLUMN type;

-- Capitalize first letter of all symbol values in elements table
UPDATE elements
SET symbol = INITCAP(symbol);

-- Change atomic_mass column to DECIMAL and remove trailing zeros
ALTER TABLE elements
  ALTER COLUMN atomic_mass TYPE DECIMAL;

-- Update atomic_mass values to match atomic_mass.txt (assuming atomic_mass.txt has correct values)
-- For simplicity, update atomic_mass values based on atomic_mass.txt content
-- (This step requires reading atomic_mass.txt and updating accordingly, will be done manually or by script)

-- Add element with atomic number 9 (Fluorine)
INSERT INTO elements (atomic_number, name, symbol)
VALUES (9, 'Fluorine', 'F');

INSERT INTO properties (atomic_number, atomic_mass, melting_point_celsius, boiling_point_celsius, type_id)
VALUES (
  9,
  18.998,
  -220,
  -188.1,
  (SELECT type_id FROM types WHERE type = 'nonmetal')
);

-- Add element with atomic number 10 (Neon)
INSERT INTO elements (atomic_number, name, symbol)
VALUES (10, 'Neon', 'Ne');

INSERT INTO properties (atomic_number, atomic_mass, melting_point_celsius, boiling_point_celsius, type_id)
VALUES (
  10,
  20.18,
  -248.6,
  -246.1,
  (SELECT type_id FROM types WHERE type = 'nonmetal')
);

-- Delete non-existent element with atomic_number 1000 from elements and properties
DELETE FROM properties WHERE atomic_number = 1000;
DELETE FROM elements WHERE atomic_number = 1000;
